{
    "files.exclude": {
        "**/.venv.OLD": true,
        "**/.venv": true,
        "**/.git": true,
        "**/.svn": true,
        "**/.hg": true,
        "**/.DS_Store": true,
        "**/Thumbs.db": true,
    },
    "python.analysis.diagnosticMode": "workspace",
    "python.analysis.exclude": [
        "**/.venv",
        "**/__pycache__",
        "**/.pytest_cache",
        "**/data",
        "**/.vscode",
        "**/**.Docs",
        "**/**.OLD",
    ],
    "pylint.enabled": true,
    "pylint.args": [
        //"--ignore-patterns=(?!<workspace_root>/.*)",
        "--max-line-length=170",
        // PyLint Messages: http://pylint-messages.wikidot.com/all-codes
        "--disable=C0114,C0115,C0116",
    ],
    "ruff.nativeServer": "on",
    "git.ignoreLimitWarning": true,
}